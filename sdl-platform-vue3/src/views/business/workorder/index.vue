<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" v-show="showSearch" label-width="80px">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item label="工单标题" prop="ticketTitle">
            <el-input
              v-model="queryParams.ticketTitle"
              placeholder="请输入工单标题"
              clearable
              @keyup.enter="handleQuery"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="系统名称" prop="systemName">
            <el-input
              v-model="queryParams.systemName"
              placeholder="请输入系统名称"
              clearable
              @keyup.enter="handleQuery"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="系统类型" prop="systemType">
            <el-select v-model="queryParams.systemType" placeholder="请选择系统类型" clearable style="width: 100%">
              <el-option
                v-for="dict in sdl_system_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="工单状态" prop="ticketStatus">
            <el-select v-model="queryParams.ticketStatus" placeholder="请选择工单状态" clearable style="width: 100%">
              <el-option
                v-for="dict in sdl_ticket_status"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="创建时间" prop="dateRange">
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              style="width: 100%"
              value-format="YYYY-MM-DD"
              @change="handleDateRangeChange"
            />
          </el-form-item>
        </el-col>
        <el-col :span="18"></el-col>
        <el-col :span="6">
          <el-form-item style="margin-right: 5px">
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['business:workorder:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['business:workorder:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['business:workorder:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['business:workorder:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table 
      v-loading="loading" 
      :data="workorderList" 
      @selection-change="handleSelectionChange"
      fit
      style="width: 100%">
      
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="工单标题" align="center" prop="ticketTitle" show-overflow-tooltip />
      <el-table-column label="测试类型" align="center" prop="ticketType">
        <template #default="scope">
          <dict-tag :options="sdl_test_type" :value="scope.row.ticketType"/>
        </template>
      </el-table-column>
      <el-table-column label="所属项目" align="center" prop="projectName" show-overflow-tooltip />
      <el-table-column label="系统名称" align="center" prop="systemName" show-overflow-tooltip />
      <el-table-column label="系统类型" align="center" prop="systemType">
        <template #default="scope">
          <dict-tag :options="sdl_system_type" :value="scope.row.systemType"/>
        </template>
      </el-table-column>
      <el-table-column label="当前处理人" align="center" prop="handler" />
      <el-table-column label="期望完成时间" align="center" prop="expectFinishTime" width="120">
        <template #default="scope">
          <span>{{ parseTime(scope.row.expectFinishTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="工单状态" align="center" prop="ticketStatus">
        <template #default="scope">
          <dict-tag :options="sdl_ticket_status" :value="scope.row.ticketStatus"/>
        </template>
      </el-table-column>
      
      <el-table-column 
        label="操作" 
        align="center" 
        class-name="small-padding fixed-width" 
        width="200">
        <template #default="scope">
          <el-button link type="primary" icon="View" @click="handleView(scope.row)" v-hasPermi="['business:workorder:query']">查看</el-button>
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['business:workorder:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['business:workorder:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改测试工单对话框 -->
    <el-dialog :title="title" v-model="open" width="800px" append-to-body>
      <el-form ref="workorderRef" :model="form" :rules="rules" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="工单标题" prop="ticketTitle">
              <el-input v-model="form.ticketTitle" placeholder="请输入工单标题" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="测试类型" prop="ticketType">
              <el-select v-model="form.ticketType" placeholder="请选择测试类型" style="width: 100%">
                <el-option
                  v-for="dict in sdl_test_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="所属项目" prop="projectName">
              <el-input v-model="form.projectName" placeholder="请输入所属项目" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="系统名称" prop="systemName">
              <el-input v-model="form.systemName" placeholder="请输入系统名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="系统类型" prop="systemType">
              <el-select v-model="form.systemType" placeholder="请选择系统类型" style="width: 100%">
                <el-option
                  v-for="dict in sdl_system_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="优先级" prop="priority">
              <el-select v-model="form.priority" placeholder="请选择优先级" style="width: 100%">
                <el-option
                  v-for="dict in sdl_priority"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="工单状态" prop="ticketStatus">
              <el-select v-model="form.ticketStatus" placeholder="请选择工单状态" style="width: 100%">
                <el-option
                  v-for="dict in sdl_ticket_status"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="项目经理" prop="projectManager">
              <el-input v-model="form.projectManager" placeholder="请输入项目经理" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="软件工程师" prop="softwareEngineer">
              <el-input v-model="form.softwareEngineer" placeholder="请输入软件工程师" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="测试申请人" prop="testApplicant">
              <el-input v-model="form.testApplicant" placeholder="请输入测试申请人" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="安全对接人" prop="securityRepresentative">
              <el-input v-model="form.securityRepresentative" placeholder="请输入安全对接人" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="当前处理人" prop="handler">
              <el-input v-model="form.handler" placeholder="请输入当前处理人" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="期望完成时间" prop="expectFinishTime">
              <el-date-picker clearable
                v-model="form.expectFinishTime"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="请选择期望完成时间"
                style="width: 100%">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="实际完成时间" prop="actualFinishTime">
              <el-date-picker clearable
                v-model="form.actualFinishTime"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="请选择实际完成时间"
                style="width: 100%">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="测试环境地址" prop="testEnv">
              <el-input v-model="form.testEnv" type="textarea" :rows="2" placeholder="请输入测试环境地址" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="测试账号密码" prop="testAccount">
              <el-input v-model="form.testAccount" type="textarea" :rows="2" placeholder="请输入测试账号密码" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="代码仓库地址" prop="codeRepo">
              <el-input v-model="form.codeRepo" type="textarea" :rows="2" placeholder="请输入代码仓库地址" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="测试范围" prop="trestScope">
              <el-input v-model="form.trestScope" type="textarea" :rows="3" placeholder="请输入测试范围" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="附件信息" prop="attachment">
              <el-input v-model="form.attachment" type="textarea" :rows="2" placeholder="请输入附件信息" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input v-model="form.remark" type="textarea" :rows="3" placeholder="请输入备注信息" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Workorder">
import { listWorkorder, getWorkorder, delWorkorder, addWorkorder, updateWorkorder } from "@/api/business/workorder"
import { ref, reactive, toRefs, getCurrentInstance } from 'vue'
import { useRouter } from 'vue-router'

const { proxy } = getCurrentInstance()
const router = useRouter()
const { sdl_ticket_status, sdl_priority, sdl_test_type, sdl_system_type } = proxy.useDict('sdl_ticket_status', 'sdl_priority', 'sdl_test_type', 'sdl_system_type')

const workorderList = ref([])
const open = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const title = ref("")
const dateRange = ref([])

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    ticketTitle: null,
    systemName: null,
    systemType: null,
    ticketStatus: null,
    startCreateTime: null,
    endCreateTime: null,
  },
  rules: {
    ticketTitle: [
      { required: true, message: "工单标题不能为空", trigger: "blur" }
    ],
  }
})

const { queryParams, form, rules } = toRefs(data)

/** 日期范围选择处理 */
function handleDateRangeChange(value) {
  if (value && value.length === 2) {
    queryParams.value.startCreateTime = value[0]
    queryParams.value.endCreateTime = value[1]
  } else {
    queryParams.value.startCreateTime = null
    queryParams.value.endCreateTime = null
  }
}

/** 查看按钮操作 */
function handleView(row) {
  const ticketId = row.ticketId
  router.push({
    path: '/business/workorder/detail',
    query: { id: ticketId }
  })
}

/** 查询测试工单列表 */
function getList() {
  loading.value = true
  listWorkorder(queryParams.value).then(response => {
    workorderList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}

// 取消按钮
function cancel() {
  open.value = false
  reset()
}

// 表单重置
function reset() {
  form.value = {
    ticketId: null,
    ticketTitle: null,
    ticketType: null,
    projectName: null,
    systemName: null,
    projectManager: null,
    softwareEngineer: null,
    testApplicant: null,
    securityRepresentative: null,
    handler: null,
    testEnv: null,
    testAccount: null,
    codeRepo: null,
    trestScope: null,
    attachment: null,
    expectFinishTime: null,
    actualFinishTime: null,
    priority: null,
    ticketStatus: null,
    createBy: null,
    createTime: null,
    updateBy: null,
    updateTime: null,
    remark: null
  }
  proxy.resetForm("workorderRef")
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = []
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    ticketTitle: null,
    systemName: null,
    ticketStatus: null,
    startCreateTime: null,
    endCreateTime: null,
  }
  proxy.resetForm("queryRef")
  handleQuery()
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.ticketId)
  single.value = selection.length != 1
  multiple.value = !selection.length
}

/** 新增按钮操作 */
function handleAdd() {
  reset()
  open.value = true
  title.value = "添加测试工单"
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset()
  const _ticketId = row.ticketId || ids.value
  getWorkorder(_ticketId).then(response => {
    form.value = response.data
    open.value = true
    title.value = "修改测试工单"
  })
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["workorderRef"].validate(valid => {
    if (valid) {
      if (form.value.ticketId != null) {
        updateWorkorder(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功")
          open.value = false
          getList()
        })
      } else {
        addWorkorder(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功")
          open.value = false
          getList()
        })
      }
    }
  })
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ticketIds = row.ticketId || ids.value
  proxy.$modal.confirm('是否确认删除测试工单编号为"' + _ticketIds + '"的数据项？').then(function() {
    return delWorkorder(_ticketIds)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {})
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('business/workorder/export', {
    ...queryParams.value
  }, `workorder_${new Date().getTime()}.xlsx`)
}

getList()
</script>
