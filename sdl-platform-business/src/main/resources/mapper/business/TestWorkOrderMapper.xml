<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.leapmotor.sdl.business.mapper.TestWorkOrderMapper">
    
    <resultMap type="TestWorkOrder" id="TestWorkOrderResult">
        <result property="ticketId"    column="ticket_id"    />
        <result property="ticketTitle"    column="ticket_title"    />
        <result property="ticketType"    column="ticket_type"    />
        <result property="projectName"    column="project_name"    />
        <result property="systemName"    column="system_name"    />
        <result property="systemType"    column="system_type"    />
        <result property="projectManager"    column="project_manager"    />
        <result property="softwareEngineer"    column="software_engineer"    />
        <result property="testApplicant"    column="test_applicant"    />
        <result property="securityRepresentative"    column="security_representative"    />
        <result property="handler"    column="handler"    />
        <result property="testEnv"    column="test_env"    />
        <result property="testAccount"    column="test_account"    />
        <result property="codeRepo"    column="code_repo"    />
        <result property="trestScope"    column="trest_scope"    />
        <result property="attachment"    column="attachment"    />
        <result property="expectFinishTime"    column="expect_finish_time"    />
        <result property="actualFinishTime"    column="actual_finish_time"    />
        <result property="priority"    column="priority"    />
        <result property="ticketStatus"    column="ticket_status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectTestWorkOrderVo">
        select ticket_id, ticket_title, ticket_type, project_name, system_name, system_type, project_manager, software_engineer, test_applicant, security_representative, handler, test_env, test_account, code_repo, trest_scope, attachment, expect_finish_time, actual_finish_time, priority, ticket_status, create_by, create_time, update_by, update_time, remark from sdl_test_workorder
    </sql>

    <select id="selectTestWorkOrderList" parameterType="TestWorkOrder" resultMap="TestWorkOrderResult">
        <include refid="selectTestWorkOrderVo"/>
        <where>  
            <if test="ticketTitle != null  and ticketTitle != ''"> and ticket_title = #{ticketTitle}</if>
            <if test="ticketType != null  and ticketType != ''"> and ticket_type = #{ticketType}</if>
            <if test="projectName != null  and projectName != ''"> and project_name like concat('%', #{projectName}, '%')</if>
            <if test="systemName != null  and systemName != ''"> and system_name like concat('%', #{systemName}, '%')</if>
            <if test="systemType != null  and systemType != ''"> and system_type = #{systemType}</if>
            <if test="projectManager != null  and projectManager != ''"> and project_manager = #{projectManager}</if>
            <if test="softwareEngineer != null  and softwareEngineer != ''"> and software_engineer = #{softwareEngineer}</if>
            <if test="testApplicant != null  and testApplicant != ''"> and test_applicant = #{testApplicant}</if>
            <if test="securityRepresentative != null  and securityRepresentative != ''"> and security_representative = #{securityRepresentative}</if>
            <if test="handler != null  and handler != ''"> and handler = #{handler}</if>
            <if test="testEnv != null  and testEnv != ''"> and test_env = #{testEnv}</if>
            <if test="testAccount != null  and testAccount != ''"> and test_account = #{testAccount}</if>
            <if test="codeRepo != null  and codeRepo != ''"> and code_repo = #{codeRepo}</if>
            <if test="trestScope != null  and trestScope != ''"> and trest_scope = #{trestScope}</if>
            <if test="attachment != null  and attachment != ''"> and attachment = #{attachment}</if>
            <if test="expectFinishTime != null "> and expect_finish_time = #{expectFinishTime}</if>
            <if test="actualFinishTime != null "> and actual_finish_time = #{actualFinishTime}</if>
            <if test="priority != null  and priority != ''"> and priority = #{priority}</if>
            <if test="ticketStatus != null  and ticketStatus != ''"> and ticket_status = #{ticketStatus}</if>
            <if test="startCreateTime != null and startCreateTime != ''"><!-- 开始时间检索 -->
                and date_format(create_time,'%Y-%m-%d') &gt;= #{startCreateTime}
            </if>
            <if test="endCreateTime != null and endCreateTime != ''"><!-- 结束时间检索 -->
                and date_format(create_time,'%Y-%m-%d') &lt;= #{endCreateTime}
            </if>
        </where>
    </select>
    
    <select id="selectTestWorkOrderByTicketId" parameterType="Long" resultMap="TestWorkOrderResult">
        <include refid="selectTestWorkOrderVo"/>
        where ticket_id = #{ticketId}
    </select>

    <insert id="insertTestWorkOrder" parameterType="TestWorkOrder">
        insert into sdl_test_workorder
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="ticketId != null">ticket_id,</if>
            <if test="ticketTitle != null and ticketTitle != ''">ticket_title,</if>
            <if test="ticketType != null">ticket_type,</if>
            <if test="projectName != null">project_name,</if>
            <if test="systemName != null">system_name,</if>
            <if test="systemType != null">system_type,</if>
            <if test="projectManager != null">project_manager,</if>
            <if test="softwareEngineer != null">software_engineer,</if>
            <if test="testApplicant != null">test_applicant,</if>
            <if test="securityRepresentative != null">security_representative,</if>
            <if test="handler != null">handler,</if>
            <if test="testEnv != null">test_env,</if>
            <if test="testAccount != null">test_account,</if>
            <if test="codeRepo != null">code_repo,</if>
            <if test="trestScope != null">trest_scope,</if>
            <if test="attachment != null">attachment,</if>
            <if test="expectFinishTime != null">expect_finish_time,</if>
            <if test="actualFinishTime != null">actual_finish_time,</if>
            <if test="priority != null">priority,</if>
            <if test="ticketStatus != null">ticket_status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="ticketId != null">#{ticketId},</if>
            <if test="ticketTitle != null and ticketTitle != ''">#{ticketTitle},</if>
            <if test="ticketType != null">#{ticketType},</if>
            <if test="projectName != null">#{projectName},</if>
            <if test="systemName != null">#{systemName},</if>
            <if test="systemType != null">#{systemType},</if>
            <if test="projectManager != null">#{projectManager},</if>
            <if test="softwareEngineer != null">#{softwareEngineer},</if>
            <if test="testApplicant != null">#{testApplicant},</if>
            <if test="securityRepresentative != null">#{securityRepresentative},</if>
            <if test="handler != null">#{handler},</if>
            <if test="testEnv != null">#{testEnv},</if>
            <if test="testAccount != null">#{testAccount},</if>
            <if test="codeRepo != null">#{codeRepo},</if>
            <if test="trestScope != null">#{trestScope},</if>
            <if test="attachment != null">#{attachment},</if>
            <if test="expectFinishTime != null">#{expectFinishTime},</if>
            <if test="actualFinishTime != null">#{actualFinishTime},</if>
            <if test="priority != null">#{priority},</if>
            <if test="ticketStatus != null">#{ticketStatus},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateTestWorkOrder" parameterType="TestWorkOrder">
        update sdl_test_workorder
        <trim prefix="SET" suffixOverrides=",">
            <if test="ticketTitle != null and ticketTitle != ''">ticket_title = #{ticketTitle},</if>
            <if test="ticketType != null">ticket_type = #{ticketType},</if>
            <if test="projectName != null">project_name = #{projectName},</if>
            <if test="systemName != null">system_name = #{systemName},</if>
            <if test="systemType != null">system_type = #{systemType},</if>
            <if test="projectManager != null">project_manager = #{projectManager},</if>
            <if test="softwareEngineer != null">software_engineer = #{softwareEngineer},</if>
            <if test="testApplicant != null">test_applicant = #{testApplicant},</if>
            <if test="securityRepresentative != null">security_representative = #{securityRepresentative},</if>
            <if test="handler != null">handler = #{handler},</if>
            <if test="testEnv != null">test_env = #{testEnv},</if>
            <if test="testAccount != null">test_account = #{testAccount},</if>
            <if test="codeRepo != null">code_repo = #{codeRepo},</if>
            <if test="trestScope != null">trest_scope = #{trestScope},</if>
            <if test="attachment != null">attachment = #{attachment},</if>
            <if test="expectFinishTime != null">expect_finish_time = #{expectFinishTime},</if>
            <if test="actualFinishTime != null">actual_finish_time = #{actualFinishTime},</if>
            <if test="priority != null">priority = #{priority},</if>
            <if test="ticketStatus != null">ticket_status = #{ticketStatus},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where ticket_id = #{ticketId}
    </update>

    <delete id="deleteTestWorkOrderByTicketId" parameterType="Long">
        delete from sdl_test_workorder where ticket_id = #{ticketId}
    </delete>

    <delete id="deleteTestWorkOrderByTicketIds" parameterType="String">
        delete from sdl_test_workorder where ticket_id in
        <foreach item="ticketId" collection="array" open="(" separator="," close=")">
            #{ticketId}
        </foreach>
    </delete>
</mapper>